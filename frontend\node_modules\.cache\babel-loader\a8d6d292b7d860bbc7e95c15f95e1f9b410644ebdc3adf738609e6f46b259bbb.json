{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\kbc-mall\\\\frontend\\\\src\\\\components\\\\BusinessCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useCallback } from \"react\";\nimport data from \"./data.json\";\nimport \"./BusinessCard.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BusinessCard = () => {\n  _s();\n  const [visibleBusinesses, setVisibleBusinesses] = useState(12);\n  const [selectedBusiness, setSelectedBusiness] = useState(null);\n  const [activeFilter, setActiveFilter] = useState(\"all\");\n  const [isLoading, setIsLoading] = useState(false);\n  const formatPhoneNumber = phoneNumber => {\n    if (!phoneNumber) return \"\";\n    const cleaned = phoneNumber.replace(/\\D/g, \"\");\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\n    return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phoneNumber;\n  };\n  const [search, setSearch] = useState({\n    name: \"\",\n    suiteNumber: \"\",\n    contactNumber: \"\",\n    storeNumber: \"\"\n  });\n  const handleSearchChange = useCallback(e => {\n    setSearch(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }));\n  }, []);\n  const handleFilterChange = useCallback(filter => {\n    setActiveFilter(filter);\n  }, []);\n  const filteredBusinesses = useMemo(() => {\n    return data.businesses.filter(business => {\n      const searchMatch = Object.keys(search).every(key => {\n        var _business$key;\n        return (_business$key = business[key]) === null || _business$key === void 0 ? void 0 : _business$key.toString().toLowerCase().includes(search[key].toLowerCase());\n      });\n      const floorMatch = activeFilter === \"all\" || activeFilter === \"floor1\" && business.floor === 1 || activeFilter === \"floor2\" && business.floor === 2 || activeFilter === \"floor3\" && business.floor === 3 || activeFilter === \"floor4\" && business.floor === 4;\n      return searchMatch && floorMatch;\n    });\n  }, [search, activeFilter]);\n  const loadMore = useCallback(() => {\n    setVisibleBusinesses(prev => Math.min(prev + 12, data.businesses.length));\n  }, []);\n  const floors = useMemo(() => [...new Set(data.businesses.map(b => b.floor))].sort(), []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"business-directory\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section bg-white p-4 rounded-3 shadow-sm mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-3 mb-md-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-search me-2 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), \"Find Businesses\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group me-3\",\n              style: {\n                maxWidth: \"300px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"input-group-text bg-white border-end-0\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-filter text-muted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select border-start-0\",\n                value: activeFilter,\n                onChange: e => handleFilterChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline-secondary\",\n              onClick: () => {\n                setSearch({\n                  name: \"\",\n                  suiteNumber: \"\",\n                  contactNumber: \"\",\n                  storeNumber: \"\"\n                });\n                setActiveFilter(\"all\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fa fa-refresh me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), \"Reset\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-3\",\n          children: Object.keys(search).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group input-group-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"input-group-text bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fa fa-${key === \"name\" ? \"building text-primary\" : key === \"suiteNumber\" ? \"map-marker text-danger\" : key === \"contactNumber\" ? \"phone text-success\" : \"hashtag text-warning\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control border-start-0\",\n                placeholder: `${key.replace(/([A-Z])/g, \" $1\")}...`,\n                name: key,\n                value: search[key],\n                onChange: handleSearchChange,\n                style: {\n                  fontSize: \"12px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted me-2\",\n            children: \"Quick search:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), [\"Insurance\", \"Money Transfer\", \"Restaurant\", \"Salon\", \"Grocery\", \"Travel\", \"Transportation\"].map(term => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-outline-primary rounded-pill me-2 mb-1\",\n            onClick: () => setSearch({\n              ...search,\n              name: term\n            }),\n            children: term\n          }, term, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-bar bg-light p-3 rounded-3 mb-4 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: data.businesses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Total Businesses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: categories.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: new Set(data.businesses.map(b => b.floor)).size\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fa fa-layer-group me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), \"Floors\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: filteredBusinesses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Matches Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\",\n        children: filteredBusinesses.slice(0, visibleBusinesses).map(business => {\n          let logoPath = business.logo.startsWith(\"http\") ? business.logo : require(`../assets/${business.logo}`);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card h-100 border-0 shadow-sm hover-shadow transition-all\",\n              onClick: () => setSelectedBusiness(business),\n              style: {\n                cursor: \"pointer\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [logoPath ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: logoPath,\n                    alt: business.name,\n                    className: \"me-3 rounded\",\n                    title: business.name,\n                    style: {\n                      width: \"60px\",\n                      height: \"60px\",\n                      objectFit: \"contain\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-light d-flex align-items-center justify-content-center me-3 rounded\",\n                    style: {\n                      width: \"60px\",\n                      height: \"60px\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa fa-building-o fa-2x\",\n                      style: {\n                        color: \"#6c757d\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-grow-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"mb-1\",\n                      children: business.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted small mb-1\",\n                      children: business.category && /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-tag me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-map-marker me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 255,\n                        columnNumber: 27\n                      }, this), business.suiteNumber]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-footer bg-transparent border-top\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa fa-phone me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this), formatPhoneNumber(business.contactNumber)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small\",\n                    children: [\"Floor \", business.floor]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, business.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), visibleBusinesses < filteredBusinesses.length && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-lg px-5 rounded-pill\",\n          onClick: loadMore,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fa fa-refresh me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), \"Load More Businesses\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), filteredBusinesses.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-5 my-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fa fa-frown-o fa-5x text-muted mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Businesses Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Try adjusting your search criteria to find what you're looking for.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-primary\",\n          onClick: () => {\n            setSearch({\n              name: \"\",\n              suiteNumber: \"\",\n              contactNumber: \"\",\n              storeNumber: \"\"\n            });\n            setActiveFilter(\"all\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fa fa-times me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), \"Clear Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), selectedBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal fade show d-block\",\n        tabIndex: \"-1\",\n        role: \"dialog\",\n        style: {\n          backgroundColor: \"rgba(0, 0, 0, 0.5)\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-dialog modal-dialog-centered modal-lg\",\n          role: \"document\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-header bg-primary text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"modal-title\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-building-o me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), selectedBusiness.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn-close btn-close-white\",\n                onClick: () => setSelectedBusiness(null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-4 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedBusiness.logo.startsWith(\"http\") ? selectedBusiness.logo : require(`../assets/${selectedBusiness.logo}`),\n                    alt: selectedBusiness.name,\n                    className: \"img-fluid rounded mb-3 border p-2\",\n                    style: {\n                      maxHeight: \"200px\",\n                      objectFit: \"contain\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-grid gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `tel:${selectedBusiness.contactNumber}`,\n                      className: \"btn btn-outline-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-phone me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 27\n                      }, this), \"Call Now\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `mailto:${selectedBusiness.email}`,\n                      className: \"btn btn-outline-secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-envelope me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 27\n                      }, this), \"Email\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"border-bottom pb-2\",\n                      children: \"Business Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-md-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-map-marker text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 378,\n                              columnNumber: 33\n                            }, this), \"Suite:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 377,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.suiteNumber]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 376,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-building text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 385,\n                              columnNumber: 33\n                            }, this), \"Floor:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 384,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.floor]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 383,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 375,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-md-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-phone text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 394,\n                              columnNumber: 33\n                            }, this), \"Phone:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 393,\n                            columnNumber: 31\n                          }, this), \" \", formatPhoneNumber(selectedBusiness.contactNumber)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 392,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-envelope text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 403,\n                              columnNumber: 33\n                            }, this), \"Email:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 402,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.email || \"N/A\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 401,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 391,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"border-bottom pb-2\",\n                      children: \"About\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: selectedBusiness.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-secondary\",\n                onClick: () => setSelectedBusiness(null),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-times me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), \"Close\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-share-alt me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), \"Share\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(BusinessCard, \"n0hCMfqVPkk8nNpUV8QHhk5Sr+c=\");\n_c = BusinessCard;\nexport default BusinessCard;\nvar _c;\n$RefreshReg$(_c, \"BusinessCard\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useCallback", "data", "jsxDEV", "_jsxDEV", "BusinessCard", "_s", "visibleBusinesses", "setVisibleBusinesses", "selectedBusiness", "setSelectedBusiness", "activeFilter", "setActiveFilter", "isLoading", "setIsLoading", "formatPhoneNumber", "phoneNumber", "cleaned", "replace", "match", "search", "setSearch", "name", "suiteNumber", "contactNumber", "storeNumber", "handleSearchChange", "e", "prev", "target", "value", "handleFilterChange", "filter", "filteredBusinesses", "businesses", "business", "searchMatch", "Object", "keys", "every", "key", "_business$key", "toString", "toLowerCase", "includes", "floorMatch", "floor", "loadMore", "Math", "min", "length", "floors", "Set", "map", "b", "sort", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "max<PERSON><PERSON><PERSON>", "onChange", "categories", "category", "onClick", "type", "placeholder", "fontSize", "term", "size", "slice", "logoPath", "logo", "startsWith", "require", "cursor", "src", "alt", "title", "width", "height", "objectFit", "color", "id", "tabIndex", "role", "backgroundColor", "maxHeight", "href", "email", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/kbc-mall/frontend/src/components/BusinessCard.js"], "sourcesContent": ["import React, { useState, useMemo, useCallback } from \"react\";\r\nimport data from \"./data.json\";\r\nimport \"./BusinessCard.css\";\r\n\r\nconst BusinessCard = () => {\r\n  const [visibleBusinesses, setVisibleBusinesses] = useState(12);\r\n  const [selectedBusiness, setSelectedBusiness] = useState(null);\r\n  const [activeFilter, setActiveFilter] = useState(\"all\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const formatPhoneNumber = (phoneNumber) => {\r\n    if (!phoneNumber) return \"\";\r\n    const cleaned = phoneNumber.replace(/\\D/g, \"\");\r\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\r\n    return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phoneNumber;\r\n  };\r\n\r\n  const [search, setSearch] = useState({\r\n    name: \"\",\r\n    suiteNumber: \"\",\r\n    contactNumber: \"\",\r\n    storeNumber: \"\",\r\n  });\r\n\r\n  const handleSearchChange = useCallback((e) => {\r\n    setSearch((prev) => ({ ...prev, [e.target.name]: e.target.value }));\r\n  }, []);\r\n\r\n  const handleFilterChange = useCallback((filter) => {\r\n    setActiveFilter(filter);\r\n  }, []);\r\n\r\n  const filteredBusinesses = useMemo(() => {\r\n    return data.businesses.filter((business) => {\r\n      const searchMatch = Object.keys(search).every((key) =>\r\n        business[key]\r\n          ?.toString()\r\n          .toLowerCase()\r\n          .includes(search[key].toLowerCase())\r\n      );\r\n\r\n      const floorMatch =\r\n        activeFilter === \"all\" ||\r\n        (activeFilter === \"floor1\" && business.floor === 1) ||\r\n        (activeFilter === \"floor2\" && business.floor === 2) ||\r\n        (activeFilter === \"floor3\" && business.floor === 3) ||\r\n        (activeFilter === \"floor4\" && business.floor === 4);\r\n\r\n      return searchMatch && floorMatch;\r\n    });\r\n  }, [search, activeFilter]);\r\n\r\n  const loadMore = useCallback(() => {\r\n    setVisibleBusinesses((prev) => Math.min(prev + 12, data.businesses.length));\r\n  }, []);\r\n\r\n  const floors = useMemo(\r\n    () => [...new Set(data.businesses.map((b) => b.floor))].sort(),\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"business-directory\">\r\n      <div className=\"container py-4\">\r\n        {/* Enhanced Search Section */}\r\n        <div className=\"search-section bg-white p-4 rounded-3 shadow-sm mb-4\">\r\n          <div className=\"d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-3\">\r\n            <h4 className=\"mb-3 mb-md-0\">\r\n              <i className=\"fa fa-search me-2 text-primary\"></i>Find Businesses\r\n            </h4>\r\n            <div className=\"d-flex align-items-center\">\r\n              <div className=\"input-group me-3\" style={{ maxWidth: \"300px\" }}>\r\n                <span className=\"input-group-text bg-white border-end-0\">\r\n                  <i className=\"fa fa-filter text-muted\"></i>\r\n                </span>\r\n                <select\r\n                  className=\"form-select border-start-0\"\r\n                  value={activeFilter}\r\n                  onChange={(e) => handleFilterChange(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All Categories</option>\r\n                  {categories.map((category) => (\r\n                    <option key={category} value={category}>\r\n                      {category}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <button\r\n                className=\"btn btn-outline-secondary\"\r\n                onClick={() => {\r\n                  setSearch({\r\n                    name: \"\",\r\n                    suiteNumber: \"\",\r\n                    contactNumber: \"\",\r\n                    storeNumber: \"\",\r\n                  });\r\n                  setActiveFilter(\"all\");\r\n                }}\r\n              >\r\n                <i className=\"fa fa-refresh me-1\"></i>Reset\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"row g-3\">\r\n            {Object.keys(search).map((key) => (\r\n              <div key={key} className=\"col-md-3\">\r\n                <div className=\"input-group input-group-lg\">\r\n                  <span className=\"input-group-text bg-white\">\r\n                    <i\r\n                      className={`fa fa-${\r\n                        key === \"name\"\r\n                          ? \"building text-primary\"\r\n                          : key === \"suiteNumber\"\r\n                          ? \"map-marker text-danger\"\r\n                          : key === \"contactNumber\"\r\n                          ? \"phone text-success\"\r\n                          : \"hashtag text-warning\"\r\n                      }`}\r\n                    ></i>\r\n                  </span>\r\n                  <input\r\n                    type=\"text\"\r\n                    className=\"form-control border-start-0\"\r\n                    placeholder={`${key.replace(/([A-Z])/g, \" $1\")}...`}\r\n                    name={key}\r\n                    value={search[key]}\r\n                    onChange={handleSearchChange}\r\n                    style={{ fontSize: \"12px\" }}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"mt-3\">\r\n            <small className=\"text-muted me-2\">Quick search:</small>\r\n            {[\r\n              \"Insurance\",\r\n              \"Money Transfer\",\r\n              \"Restaurant\",\r\n              \"Salon\",\r\n              \"Grocery\",\r\n              \"Travel\",\r\n              \"Transportation\",\r\n            ].map((term) => (\r\n              <button\r\n                key={term}\r\n                className=\"btn btn-sm btn-outline-primary rounded-pill me-2 mb-1\"\r\n                onClick={() => setSearch({ ...search, name: term })}\r\n              >\r\n                {term}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Bar */}\r\n        <div className=\"stats-bar bg-light p-3 rounded-3 mb-4 shadow-sm\">\r\n          <div className=\"row text-center\">\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {data.businesses.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-building-o me-1\"></i> */}\r\n                Total Businesses\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {categories.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-tags me-1\"></i> */}\r\n                Categories\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {new Set(data.businesses.map((b) => b.floor)).size}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                <i className=\"fa fa-layer-group me-1\"></i>\r\n                Floors\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {filteredBusinesses.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-check-circle me-1\"></i> */}\r\n                Matches Found\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Business Cards */}\r\n        <div className=\"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\">\r\n          {filteredBusinesses.slice(0, visibleBusinesses).map((business) => {\r\n            let logoPath = business.logo.startsWith(\"http\")\r\n              ? business.logo\r\n              : require(`../assets/${business.logo}`);\r\n\r\n            return (\r\n              <div key={business.id} className=\"col\">\r\n                <div\r\n                  className=\"card h-100 border-0 shadow-sm hover-shadow transition-all\"\r\n                  onClick={() => setSelectedBusiness(business)}\r\n                  style={{ cursor: \"pointer\" }}\r\n                >\r\n                  <div className=\"card-body\">\r\n                    <div className=\"d-flex align-items-center\">\r\n                      {logoPath ? (\r\n                        <img\r\n                          src={logoPath}\r\n                          alt={business.name}\r\n                          className=\"me-3 rounded\"\r\n                          title={business.name}\r\n                          style={{\r\n                            width: \"60px\",\r\n                            height: \"60px\",\r\n                            objectFit: \"contain\",\r\n                          }}\r\n                        />\r\n                      ) : (\r\n                        <div\r\n                          className=\"bg-light d-flex align-items-center justify-content-center me-3 rounded\"\r\n                          style={{\r\n                            width: \"60px\",\r\n                            height: \"60px\",\r\n                          }}\r\n                        >\r\n                          <i\r\n                            className=\"fa fa-building-o fa-2x\"\r\n                            style={{ color: \"#6c757d\" }}\r\n                          ></i>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"flex-grow-1\">\r\n                        <h5 className=\"mb-1\">{business.name}</h5>\r\n                        <p className=\"text-muted small mb-1\">\r\n                          {business.category && (\r\n                            <i className=\"fa fa-tag me-1\"></i>\r\n                          )}\r\n\r\n                          {/* {business.category} */}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text-end\">\r\n                        <span className=\"badge bg-primary\">\r\n                          <i className=\"fa fa-map-marker me-1\"></i>\r\n                          {business.suiteNumber}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"card-footer bg-transparent border-top\">\r\n                    <div className=\"d-flex justify-content-between\">\r\n                      <span className=\"small\">\r\n                        <i className=\"fa fa-phone me-1\"></i>\r\n                        {formatPhoneNumber(business.contactNumber)}\r\n                      </span>\r\n                      <span className=\"small\">\r\n                        {/* <i className=\"fa fa-map me-1\"></i> */}\r\n                        Floor {business.floor}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {visibleBusinesses < filteredBusinesses.length && (\r\n          <div className=\"text-center mt-4\">\r\n            <button\r\n              className=\"btn btn-primary btn-lg px-5 rounded-pill\"\r\n              onClick={loadMore}\r\n            >\r\n              <i className=\"fa fa-refresh me-2\"></i>Load More Businesses\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* No Results */}\r\n        {filteredBusinesses.length === 0 && (\r\n          <div className=\"text-center py-5 my-5\">\r\n            <i className=\"fa fa-frown-o fa-5x text-muted mb-4\"></i>\r\n            <h3>No Businesses Found</h3>\r\n            <p className=\"text-muted\">\r\n              Try adjusting your search criteria to find what you're looking\r\n              for.\r\n            </p>\r\n            <button\r\n              className=\"btn btn-outline-primary\"\r\n              onClick={() => {\r\n                setSearch({\r\n                  name: \"\",\r\n                  suiteNumber: \"\",\r\n                  contactNumber: \"\",\r\n                  storeNumber: \"\",\r\n                });\r\n                setActiveFilter(\"all\");\r\n              }}\r\n            >\r\n              <i className=\"fa fa-times me-1\"></i>Clear Filters\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Business Modal */}\r\n        {selectedBusiness && (\r\n          <div\r\n            className=\"modal fade show d-block\"\r\n            tabIndex=\"-1\"\r\n            role=\"dialog\"\r\n            style={{ backgroundColor: \"rgba(0, 0, 0, 0.5)\" }}\r\n          >\r\n            <div\r\n              className=\"modal-dialog modal-dialog-centered modal-lg\"\r\n              role=\"document\"\r\n            >\r\n              <div className=\"modal-content\">\r\n                <div className=\"modal-header bg-primary text-white\">\r\n                  <h3 className=\"modal-title\">\r\n                    <i className=\"fa fa-building-o me-2\"></i>\r\n                    {selectedBusiness.name}\r\n                  </h3>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"btn-close btn-close-white\"\r\n                    onClick={() => setSelectedBusiness(null)}\r\n                  ></button>\r\n                </div>\r\n                <div className=\"modal-body\">\r\n                  <div className=\"row\">\r\n                    <div className=\"col-md-4 text-center\">\r\n                      <img\r\n                        src={\r\n                          selectedBusiness.logo.startsWith(\"http\")\r\n                            ? selectedBusiness.logo\r\n                            : require(`../assets/${selectedBusiness.logo}`)\r\n                        }\r\n                        alt={selectedBusiness.name}\r\n                        className=\"img-fluid rounded mb-3 border p-2\"\r\n                        style={{\r\n                          maxHeight: \"200px\",\r\n                          objectFit: \"contain\",\r\n                        }}\r\n                      />\r\n                      <div className=\"d-grid gap-2\">\r\n                        <a\r\n                          href={`tel:${selectedBusiness.contactNumber}`}\r\n                          className=\"btn btn-outline-primary\"\r\n                        >\r\n                          <i className=\"fa fa-phone me-2\"></i>Call Now\r\n                        </a>\r\n                        <a\r\n                          href={`mailto:${selectedBusiness.email}`}\r\n                          className=\"btn btn-outline-secondary\"\r\n                        >\r\n                          <i className=\"fa fa-envelope me-2\"></i>Email\r\n                        </a>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"col-md-8\">\r\n                      <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">Business Details</h4>\r\n                        <div className=\"row\">\r\n                          <div className=\"col-md-6\">\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-map-marker text-primary me-2\"></i>\r\n                                Suite:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.suiteNumber}\r\n                            </p>\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-building text-primary me-2\"></i>\r\n                                Floor:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.floor}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"col-md-6\">\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-phone text-primary me-2\"></i>\r\n                                Phone:\r\n                              </strong>{\" \"}\r\n                              {formatPhoneNumber(\r\n                                selectedBusiness.contactNumber\r\n                              )}\r\n                            </p>\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-envelope text-primary me-2\"></i>\r\n                                Email:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.email || \"N/A\"}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">About</h4>\r\n                        <p>{selectedBusiness.description}</p>\r\n                      </div>\r\n\r\n                      {/* <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">Category</h4>\r\n                        <span className=\"badge bg-primary\">\r\n                          {selectedBusiness.category}\r\n                        </span>\r\n                      </div> */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"modal-footer\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"btn btn-secondary\"\r\n                    onClick={() => setSelectedBusiness(null)}\r\n                  >\r\n                    <i className=\"fa fa-times me-1\"></i>Close\r\n                  </button>\r\n                  <button type=\"button\" className=\"btn btn-primary\">\r\n                    <i className=\"fa fa-share-alt me-1\"></i>Share\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default BusinessCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7D,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMgB,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAC3B,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,MAAMC,KAAK,GAAGF,OAAO,CAACE,KAAK,CAAC,yBAAyB,CAAC;IACtD,OAAOA,KAAK,GAAG,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGH,WAAW;EACtE,CAAC;EAED,MAAM,CAACI,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC;IACnCuB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAGzB,WAAW,CAAE0B,CAAC,IAAK;IAC5CN,SAAS,CAAEO,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACD,CAAC,CAACE,MAAM,CAACP,IAAI,GAAGK,CAAC,CAACE,MAAM,CAACC;IAAM,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAG9B,WAAW,CAAE+B,MAAM,IAAK;IACjDpB,eAAe,CAACoB,MAAM,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGjC,OAAO,CAAC,MAAM;IACvC,OAAOE,IAAI,CAACgC,UAAU,CAACF,MAAM,CAAEG,QAAQ,IAAK;MAC1C,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAClB,MAAM,CAAC,CAACmB,KAAK,CAAEC,GAAG;QAAA,IAAAC,aAAA;QAAA,QAAAA,aAAA,GAChDN,QAAQ,CAACK,GAAG,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CACIC,QAAQ,CAAC,CAAC,CACXC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACxB,MAAM,CAACoB,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;MAAA,CACxC,CAAC;MAED,MAAME,UAAU,GACdlC,YAAY,KAAK,KAAK,IACrBA,YAAY,KAAK,QAAQ,IAAIwB,QAAQ,CAACW,KAAK,KAAK,CAAE,IAClDnC,YAAY,KAAK,QAAQ,IAAIwB,QAAQ,CAACW,KAAK,KAAK,CAAE,IAClDnC,YAAY,KAAK,QAAQ,IAAIwB,QAAQ,CAACW,KAAK,KAAK,CAAE,IAClDnC,YAAY,KAAK,QAAQ,IAAIwB,QAAQ,CAACW,KAAK,KAAK,CAAE;MAErD,OAAOV,WAAW,IAAIS,UAAU;IAClC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,MAAM,EAAET,YAAY,CAAC,CAAC;EAE1B,MAAMoC,QAAQ,GAAG9C,WAAW,CAAC,MAAM;IACjCO,oBAAoB,CAAEoB,IAAI,IAAKoB,IAAI,CAACC,GAAG,CAACrB,IAAI,GAAG,EAAE,EAAE1B,IAAI,CAACgC,UAAU,CAACgB,MAAM,CAAC,CAAC;EAC7E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,MAAM,GAAGnD,OAAO,CACpB,MAAM,CAAC,GAAG,IAAIoD,GAAG,CAAClD,IAAI,CAACgC,UAAU,CAACmB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACR,KAAK,CAAC,CAAC,CAAC,CAACS,IAAI,CAAC,CAAC,EAC9D,EACF,CAAC;EAED,oBACEnD,OAAA;IAASoD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrCrD,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7BrD,OAAA;QAAKoD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnErD,OAAA;UAAKoD,SAAS,EAAC,mFAAmF;UAAAC,QAAA,gBAChGrD,OAAA;YAAIoD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BrD,OAAA;cAAGoD,SAAS,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBACpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzD,OAAA;YAAKoD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCrD,OAAA;cAAKoD,SAAS,EAAC,kBAAkB;cAACM,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAN,QAAA,gBAC7DrD,OAAA;gBAAMoD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACtDrD,OAAA;kBAAGoD,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACPzD,OAAA;gBACEoD,SAAS,EAAC,4BAA4B;gBACtC1B,KAAK,EAAEnB,YAAa;gBACpBqD,QAAQ,EAAGrC,CAAC,IAAKI,kBAAkB,CAACJ,CAAC,CAACE,MAAM,CAACC,KAAK,CAAE;gBAAA2B,QAAA,gBAEpDrD,OAAA;kBAAQ0B,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CI,UAAU,CAACZ,GAAG,CAAEa,QAAQ,iBACvB9D,OAAA;kBAAuB0B,KAAK,EAAEoC,QAAS;kBAAAT,QAAA,EACpCS;gBAAQ,GADEA,QAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNzD,OAAA;cACEoD,SAAS,EAAC,2BAA2B;cACrCW,OAAO,EAAEA,CAAA,KAAM;gBACb9C,SAAS,CAAC;kBACRC,IAAI,EAAE,EAAE;kBACRC,WAAW,EAAE,EAAE;kBACfC,aAAa,EAAE,EAAE;kBACjBC,WAAW,EAAE;gBACf,CAAC,CAAC;gBACFb,eAAe,CAAC,KAAK,CAAC;cACxB,CAAE;cAAA6C,QAAA,gBAEFrD,OAAA;gBAAGoD,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SACxC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrBpB,MAAM,CAACC,IAAI,CAAClB,MAAM,CAAC,CAACiC,GAAG,CAAEb,GAAG,iBAC3BpC,OAAA;YAAeoD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACjCrD,OAAA;cAAKoD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCrD,OAAA;gBAAMoD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACzCrD,OAAA;kBACEoD,SAAS,EAAE,SACThB,GAAG,KAAK,MAAM,GACV,uBAAuB,GACvBA,GAAG,KAAK,aAAa,GACrB,wBAAwB,GACxBA,GAAG,KAAK,eAAe,GACvB,oBAAoB,GACpB,sBAAsB;gBACzB;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACPzD,OAAA;gBACEgE,IAAI,EAAC,MAAM;gBACXZ,SAAS,EAAC,6BAA6B;gBACvCa,WAAW,EAAE,GAAG7B,GAAG,CAACtB,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAM;gBACpDI,IAAI,EAAEkB,GAAI;gBACVV,KAAK,EAAEV,MAAM,CAACoB,GAAG,CAAE;gBACnBwB,QAAQ,EAAEtC,kBAAmB;gBAC7BoC,KAAK,EAAE;kBAAEQ,QAAQ,EAAE;gBAAO;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC,GAxBErB,GAAG;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBR,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAOoD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACvD,CACC,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,SAAS,EACT,QAAQ,EACR,gBAAgB,CACjB,CAACR,GAAG,CAAEkB,IAAI,iBACTnE,OAAA;YAEEoD,SAAS,EAAC,uDAAuD;YACjEW,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEE,IAAI,EAAEiD;YAAK,CAAC,CAAE;YAAAd,QAAA,EAEnDc;UAAI,GAJAA,IAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKH,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DrD,OAAA;UAAKoD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrD,OAAA;YAAKoD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrD,OAAA;cAAIoD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CvD,IAAI,CAACgC,UAAU,CAACgB;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACLzD,OAAA;cAAGoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACqB;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrD,OAAA;cAAIoD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CQ,UAAU,CAACf;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACLzD,OAAA;cAAGoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACe;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrD,OAAA;cAAIoD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C,IAAIL,GAAG,CAAClD,IAAI,CAACgC,UAAU,CAACmB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACR,KAAK,CAAC,CAAC,CAAC0B;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACLzD,OAAA;cAAGoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BrD,OAAA;gBAAGoD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBrD,OAAA;cAAIoD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CxB,kBAAkB,CAACiB;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACLzD,OAAA;cAAGoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACuB;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAC5DxB,kBAAkB,CAACwC,KAAK,CAAC,CAAC,EAAElE,iBAAiB,CAAC,CAAC8C,GAAG,CAAElB,QAAQ,IAAK;UAChE,IAAIuC,QAAQ,GAAGvC,QAAQ,CAACwC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,GAC3CzC,QAAQ,CAACwC,IAAI,GACbE,OAAO,CAAC,aAAa1C,QAAQ,CAACwC,IAAI,EAAE,CAAC;UAEzC,oBACEvE,OAAA;YAAuBoD,SAAS,EAAC,KAAK;YAAAC,QAAA,eACpCrD,OAAA;cACEoD,SAAS,EAAC,2DAA2D;cACrEW,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAACyB,QAAQ,CAAE;cAC7C2B,KAAK,EAAE;gBAAEgB,MAAM,EAAE;cAAU,CAAE;cAAArB,QAAA,gBAE7BrD,OAAA;gBAAKoD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBrD,OAAA;kBAAKoD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GACvCiB,QAAQ,gBACPtE,OAAA;oBACE2E,GAAG,EAAEL,QAAS;oBACdM,GAAG,EAAE7C,QAAQ,CAACb,IAAK;oBACnBkC,SAAS,EAAC,cAAc;oBACxByB,KAAK,EAAE9C,QAAQ,CAACb,IAAK;oBACrBwC,KAAK,EAAE;sBACLoB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdC,SAAS,EAAE;oBACb;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFzD,OAAA;oBACEoD,SAAS,EAAC,wEAAwE;oBAClFM,KAAK,EAAE;sBACLoB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE;oBACV,CAAE;oBAAA1B,QAAA,eAEFrD,OAAA;sBACEoD,SAAS,EAAC,wBAAwB;sBAClCM,KAAK,EAAE;wBAAEuB,KAAK,EAAE;sBAAU;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,eACDzD,OAAA;oBAAKoD,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BrD,OAAA;sBAAIoD,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEtB,QAAQ,CAACb;oBAAI;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzCzD,OAAA;sBAAGoD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACjCtB,QAAQ,CAAC+B,QAAQ,iBAChB9D,OAAA;wBAAGoD,SAAS,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAClC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACNzD,OAAA;oBAAKoD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBrD,OAAA;sBAAMoD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAChCrD,OAAA;wBAAGoD,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACxC1B,QAAQ,CAACZ,WAAW;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAKoD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDrD,OAAA;kBAAKoD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CrD,OAAA;oBAAMoD,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBACrBrD,OAAA;sBAAGoD,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnC9C,iBAAiB,CAACoB,QAAQ,CAACX,aAAa,CAAC;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACPzD,OAAA;oBAAMoD,SAAS,EAAC,OAAO;oBAAAC,QAAA,GACqB,QACpC,EAACtB,QAAQ,CAACW,KAAK;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAhEE1B,QAAQ,CAACmD,EAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiEhB,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELtD,iBAAiB,GAAG0B,kBAAkB,CAACiB,MAAM,iBAC5C9C,OAAA;QAAKoD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BrD,OAAA;UACEoD,SAAS,EAAC,0CAA0C;UACpDW,OAAO,EAAEpB,QAAS;UAAAU,QAAA,gBAElBrD,OAAA;YAAGoD,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,wBACxC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA5B,kBAAkB,CAACiB,MAAM,KAAK,CAAC,iBAC9B9C,OAAA;QAAKoD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCrD,OAAA;UAAGoD,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDzD,OAAA;UAAAqD,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BzD,OAAA;UAAGoD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAG1B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJzD,OAAA;UACEoD,SAAS,EAAC,yBAAyB;UACnCW,OAAO,EAAEA,CAAA,KAAM;YACb9C,SAAS,CAAC;cACRC,IAAI,EAAE,EAAE;cACRC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,EAAE;cACjBC,WAAW,EAAE;YACf,CAAC,CAAC;YACFb,eAAe,CAAC,KAAK,CAAC;UACxB,CAAE;UAAA6C,QAAA,gBAEFrD,OAAA;YAAGoD,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGApD,gBAAgB,iBACfL,OAAA;QACEoD,SAAS,EAAC,yBAAyB;QACnC+B,QAAQ,EAAC,IAAI;QACbC,IAAI,EAAC,QAAQ;QACb1B,KAAK,EAAE;UAAE2B,eAAe,EAAE;QAAqB,CAAE;QAAAhC,QAAA,eAEjDrD,OAAA;UACEoD,SAAS,EAAC,6CAA6C;UACvDgC,IAAI,EAAC,UAAU;UAAA/B,QAAA,eAEfrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrD,OAAA;cAAKoD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDrD,OAAA;gBAAIoD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzBrD,OAAA;kBAAGoD,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxCpD,gBAAgB,CAACa,IAAI;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACLzD,OAAA;gBACEgE,IAAI,EAAC,QAAQ;gBACbZ,SAAS,EAAC,2BAA2B;gBACrCW,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,IAAI;cAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBrD,OAAA;gBAAKoD,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBrD,OAAA;kBAAKoD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCrD,OAAA;oBACE2E,GAAG,EACDtE,gBAAgB,CAACkE,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,GACpCnE,gBAAgB,CAACkE,IAAI,GACrBE,OAAO,CAAC,aAAapE,gBAAgB,CAACkE,IAAI,EAAE,CACjD;oBACDK,GAAG,EAAEvE,gBAAgB,CAACa,IAAK;oBAC3BkC,SAAS,EAAC,mCAAmC;oBAC7CM,KAAK,EAAE;sBACL4B,SAAS,EAAE,OAAO;sBAClBN,SAAS,EAAE;oBACb;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFzD,OAAA;oBAAKoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BrD,OAAA;sBACEuF,IAAI,EAAE,OAAOlF,gBAAgB,CAACe,aAAa,EAAG;sBAC9CgC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBAEnCrD,OAAA;wBAAGoD,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YACtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJzD,OAAA;sBACEuF,IAAI,EAAE,UAAUlF,gBAAgB,CAACmF,KAAK,EAAG;sBACzCpC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBAErCrD,OAAA;wBAAGoD,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SACzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBrD,OAAA;oBAAKoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBrD,OAAA;sBAAIoD,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxDzD,OAAA;sBAAKoD,SAAS,EAAC,KAAK;sBAAAC,QAAA,gBAClBrD,OAAA;wBAAKoD,SAAS,EAAC,UAAU;wBAAAC,QAAA,gBACvBrD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,gBACErD,OAAA;8BAAGoD,SAAS,EAAC;4BAAoC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAExD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZpD,gBAAgB,CAACc,WAAW;wBAAA;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,eACJzD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,gBACErD,OAAA;8BAAGoD,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEtD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZpD,gBAAgB,CAACqC,KAAK;wBAAA;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNzD,OAAA;wBAAKoD,SAAS,EAAC,UAAU;wBAAAC,QAAA,gBACvBrD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,gBACErD,OAAA;8BAAGoD,SAAS,EAAC;4BAA+B;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEnD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZ9C,iBAAiB,CAChBN,gBAAgB,CAACe,aACnB,CAAC;wBAAA;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACJzD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,gBACErD,OAAA;8BAAGoD,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEtD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZpD,gBAAgB,CAACmF,KAAK,IAAI,KAAK;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENzD,OAAA;oBAAKoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBrD,OAAA;sBAAIoD,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7CzD,OAAA;sBAAAqD,QAAA,EAAIhD,gBAAgB,CAACoF;oBAAW;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrD,OAAA;gBACEgE,IAAI,EAAC,QAAQ;gBACbZ,SAAS,EAAC,mBAAmB;gBAC7BW,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,IAAI,CAAE;gBAAA+C,QAAA,gBAEzCrD,OAAA;kBAAGoD,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,SACtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA;gBAAQgE,IAAI,EAAC,QAAQ;gBAACZ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC/CrD,OAAA;kBAAGoD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,SAC1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACvD,EAAA,CAxbID,YAAY;AAAAyF,EAAA,GAAZzF,YAAY;AA0blB,eAAeA,YAAY;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}