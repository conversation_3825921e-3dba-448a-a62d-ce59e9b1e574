{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\kbc-mall\\\\frontend\\\\src\\\\components\\\\Header.js\";\nimport React from \"react\";\nimport kbcLogo from \"../assets/kbc-new-log.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid px-0\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-primary text-white py-4 py-md-5 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 text-center text-md-start mb-3 mb-md-0\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: kbcLogo,\n              alt: \"KBC Logo\",\n              className: \"img-fluid\",\n              style: {\n                maxHeight: \"60px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"fw-bold mb-2\",\n              children: \"Business Directory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead mb-0 d-none d-md-block\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fa fa-map-marker me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), \" Connecting you to local excellence\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "kbcLogo", "jsxDEV", "_jsxDEV", "Header", "className", "children", "src", "alt", "style", "maxHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/kbc-mall/frontend/src/components/Header.js"], "sourcesContent": ["import React from \"react\";\r\nimport kbcLogo from \"../assets/kbc-new-log.png\";\r\n\r\nconst Header = () => {\r\n  return (\r\n    <div className=\"container-fluid px-0\">\r\n      {/* Elegant Header with Subtle Shadow */}\r\n      <div className=\"bg-primary text-white py-4 py-md-5 shadow-sm\">\r\n        <div className=\"container\">\r\n          <div className=\"row align-items-center\">\r\n            {/* Logo */}\r\n            <div className=\"col-md-3 text-center text-md-start mb-3 mb-md-0\">\r\n              <img\r\n                src={kbcLogo}\r\n                alt=\"KBC Logo\"\r\n                className=\"img-fluid\"\r\n                style={{ maxHeight: \"60px\" }}\r\n              />\r\n            </div>\r\n\r\n            {/* Main Title */}\r\n            <div className=\"col-md-6 text-center\">\r\n              <h1 className=\"fw-bold mb-2\">\r\n\r\n               {/* <i className=\"fa fa-address-book-o me-2\"></i> */}\r\n                Business Directory\r\n              </h1>\r\n              <p className=\"lead mb-0 d-none d-md-block\">\r\n                <i className=\"fa fa-map-marker me-2\"></i> Connecting you to\r\n                local excellence\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAKE,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eAEnCH,OAAA;MAAKE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCH,OAAA;YAAKE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,eAC9DH,OAAA;cACEI,GAAG,EAAEN,OAAQ;cACbO,GAAG,EAAC,UAAU;cACdH,SAAS,EAAC,WAAW;cACrBI,KAAK,EAAE;gBAAEC,SAAS,EAAE;cAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCH,OAAA;cAAIE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAE0B;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAGE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxCH,OAAA;gBAAGE,SAAS,EAAC;cAAuB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uCAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAlCIX,MAAM;AAoCZ,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}