{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\kbc-mall\\\\frontend\\\\src\\\\components\\\\BusinessCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport data from \"./data.json\";\nimport \"./BusinessCard.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BusinessCard = () => {\n  _s();\n  const [visibleBusinesses, setVisibleBusinesses] = useState(12);\n  const [selectedBusiness, setSelectedBusiness] = useState(null);\n  const [activeFilter, setActiveFilter] = useState(\"all\");\n  const formatPhoneNumber = phoneNumber => {\n    if (!phoneNumber) return \"\";\n    const cleaned = phoneNumber.replace(/\\D/g, \"\");\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\n    return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phoneNumber;\n  };\n  const [search, setSearch] = useState({\n    name: \"\",\n    suiteNumber: \"\",\n    contactNumber: \"\",\n    storeNumber: \"\"\n  });\n  const handleSearchChange = e => {\n    setSearch({\n      ...search,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleFilterChange = filter => {\n    setActiveFilter(filter);\n  };\n  const filteredBusinesses = data.businesses.filter(business => {\n    const searchMatch = Object.keys(search).every(key => {\n      var _business$key;\n      return (_business$key = business[key]) === null || _business$key === void 0 ? void 0 : _business$key.toString().toLowerCase().includes(search[key].toLowerCase());\n    });\n    const categoryMatch = activeFilter === \"all\" || business.category === activeFilter;\n    return searchMatch && categoryMatch;\n  });\n  const loadMore = () => {\n    setVisibleBusinesses(prev => Math.min(prev + 12, data.businesses.length));\n  };\n  const categories = [...new Set(data.businesses.map(b => b.category))];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"business-directory\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section bg-white p-4 rounded-3 shadow-sm mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-3 mb-md-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-search me-2 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), \"Find Businesses\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group me-3\",\n              style: {\n                maxWidth: \"300px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"input-group-text bg-white border-end-0\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-filter text-muted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select border-start-0\",\n                value: activeFilter,\n                onChange: e => handleFilterChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline-secondary\",\n              onClick: () => {\n                setSearch({\n                  name: \"\",\n                  suiteNumber: \"\",\n                  contactNumber: \"\",\n                  storeNumber: \"\"\n                });\n                setActiveFilter(\"all\");\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fa fa-refresh me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), \"Reset\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-3\",\n          children: Object.keys(search).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group input-group-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"input-group-text bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fa fa-${key === \"name\" ? \"building text-primary\" : key === \"suiteNumber\" ? \"map-marker text-danger\" : key === \"contactNumber\" ? \"phone text-success\" : \"hashtag text-warning\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control border-start-0\",\n                placeholder: `${key.replace(/([A-Z])/g, \" $1\")}...`,\n                name: key,\n                value: search[key],\n                onChange: handleSearchChange,\n                style: {\n                  fontSize: \"12px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted me-2\",\n            children: \"Quick search:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), [\"Insurance\", \"Money Transfer\", \"Restaurant\", \"Salon\", \"Grocery\", \"Travel\", \"Transportation\"].map(term => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-outline-primary rounded-pill me-2 mb-1\",\n            onClick: () => setSearch({\n              ...search,\n              name: term\n            }),\n            children: term\n          }, term, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-bar bg-light p-3 rounded-3 mb-4 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: data.businesses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Total Businesses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: categories.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 border-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: new Set(data.businesses.map(b => b.floor)).size\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fa fa-layer-group me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), \"Floors\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary mb-0 animate-count\",\n              children: filteredBusinesses.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Matches Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\",\n        children: filteredBusinesses.slice(0, visibleBusinesses).map(business => {\n          let logoPath = business.logo.startsWith(\"http\") ? business.logo : require(`../assets/${business.logo}`);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card h-100 border-0 shadow-sm hover-shadow transition-all\",\n              onClick: () => setSelectedBusiness(business),\n              style: {\n                cursor: \"pointer\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [logoPath ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: logoPath,\n                    alt: business.name,\n                    className: \"me-3 rounded\",\n                    title: business.name,\n                    style: {\n                      width: \"60px\",\n                      height: \"60px\",\n                      objectFit: \"contain\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-light d-flex align-items-center justify-content-center me-3 rounded\",\n                    style: {\n                      width: \"60px\",\n                      height: \"60px\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa fa-building-o fa-2x\",\n                      style: {\n                        color: \"#6c757d\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-grow-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"mb-1\",\n                      children: business.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted small mb-1\",\n                      children: business.category && /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-tag me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-map-marker me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 27\n                      }, this), business.suiteNumber]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-footer bg-transparent border-top\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa fa-phone me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 25\n                    }, this), formatPhoneNumber(business.contactNumber)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small\",\n                    children: [\"Floor \", business.floor]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)\n          }, business.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), visibleBusinesses < filteredBusinesses.length && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-lg px-5 rounded-pill\",\n          onClick: loadMore,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fa fa-refresh me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), \"Load More Businesses\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this), filteredBusinesses.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-5 my-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fa fa-frown-o fa-5x text-muted mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Businesses Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Try adjusting your search criteria to find what you're looking for.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-primary\",\n          onClick: () => {\n            setSearch({\n              name: \"\",\n              suiteNumber: \"\",\n              contactNumber: \"\",\n              storeNumber: \"\"\n            });\n            setActiveFilter(\"all\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fa fa-times me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), \"Clear Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), selectedBusiness && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal fade show d-block\",\n        tabIndex: \"-1\",\n        role: \"dialog\",\n        style: {\n          backgroundColor: \"rgba(0, 0, 0, 0.5)\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-dialog modal-dialog-centered modal-lg\",\n          role: \"document\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-header bg-primary text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"modal-title\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-building-o me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), selectedBusiness.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn-close btn-close-white\",\n                onClick: () => setSelectedBusiness(null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-4 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedBusiness.logo.startsWith(\"http\") ? selectedBusiness.logo : require(`../assets/${selectedBusiness.logo}`),\n                    alt: selectedBusiness.name,\n                    className: \"img-fluid rounded mb-3 border p-2\",\n                    style: {\n                      maxHeight: \"200px\",\n                      objectFit: \"contain\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-grid gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `tel:${selectedBusiness.contactNumber}`,\n                      className: \"btn btn-outline-primary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-phone me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 27\n                      }, this), \"Call Now\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: `mailto:${selectedBusiness.email}`,\n                      className: \"btn btn-outline-secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa fa-envelope me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 27\n                      }, this), \"Email\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"border-bottom pb-2\",\n                      children: \"Business Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-md-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-map-marker text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 366,\n                              columnNumber: 33\n                            }, this), \"Suite:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 365,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.suiteNumber]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-building text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 373,\n                              columnNumber: 33\n                            }, this), \"Floor:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 372,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.floor]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-md-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-phone text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 382,\n                              columnNumber: 33\n                            }, this), \"Phone:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 381,\n                            columnNumber: 31\n                          }, this), \" \", formatPhoneNumber(selectedBusiness.contactNumber)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 380,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fa fa-envelope text-primary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 391,\n                              columnNumber: 33\n                            }, this), \"Email:\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 390,\n                            columnNumber: 31\n                          }, this), \" \", selectedBusiness.email || \"N/A\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 389,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"border-bottom pb-2\",\n                      children: \"About\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: selectedBusiness.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-secondary\",\n                onClick: () => setSelectedBusiness(null),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-times me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this), \"Close\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa fa-share-alt me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), \"Share\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(BusinessCard, \"oGJb3ZdpunHNl4pQNvuqYBnlU7A=\");\n_c = BusinessCard;\nexport default BusinessCard;\nvar _c;\n$RefreshReg$(_c, \"BusinessCard\");", "map": {"version": 3, "names": ["React", "useState", "data", "jsxDEV", "_jsxDEV", "BusinessCard", "_s", "visibleBusinesses", "setVisibleBusinesses", "selectedBusiness", "setSelectedBusiness", "activeFilter", "setActiveFilter", "formatPhoneNumber", "phoneNumber", "cleaned", "replace", "match", "search", "setSearch", "name", "suiteNumber", "contactNumber", "storeNumber", "handleSearchChange", "e", "target", "value", "handleFilterChange", "filter", "filteredBusinesses", "businesses", "business", "searchMatch", "Object", "keys", "every", "key", "_business$key", "toString", "toLowerCase", "includes", "categoryMatch", "category", "loadMore", "prev", "Math", "min", "length", "categories", "Set", "map", "b", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "max<PERSON><PERSON><PERSON>", "onChange", "onClick", "type", "placeholder", "fontSize", "term", "floor", "size", "slice", "logoPath", "logo", "startsWith", "require", "cursor", "src", "alt", "title", "width", "height", "objectFit", "color", "id", "tabIndex", "role", "backgroundColor", "maxHeight", "href", "email", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/kbc-mall/frontend/src/components/BusinessCard.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport data from \"./data.json\";\r\nimport \"./BusinessCard.css\";\r\n\r\nconst BusinessCard = () => {\r\n  const [visibleBusinesses, setVisibleBusinesses] = useState(12);\r\n  const [selectedBusiness, setSelectedBusiness] = useState(null);\r\n  const [activeFilter, setActiveFilter] = useState(\"all\");\r\n\r\n  const formatPhoneNumber = (phoneNumber) => {\r\n    if (!phoneNumber) return \"\";\r\n    const cleaned = phoneNumber.replace(/\\D/g, \"\");\r\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\r\n    return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phoneNumber;\r\n  };\r\n\r\n  const [search, setSearch] = useState({\r\n    name: \"\",\r\n    suiteNumber: \"\",\r\n    contactNumber: \"\",\r\n    storeNumber: \"\",\r\n  });\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearch({ ...search, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleFilterChange = (filter) => {\r\n    setActiveFilter(filter);\r\n  };\r\n\r\n  const filteredBusinesses = data.businesses.filter((business) => {\r\n    const searchMatch = Object.keys(search).every((key) =>\r\n      business[key]\r\n        ?.toString()\r\n        .toLowerCase()\r\n        .includes(search[key].toLowerCase())\r\n    );\r\n    const categoryMatch =\r\n      activeFilter === \"all\" || business.category === activeFilter;\r\n    return searchMatch && categoryMatch;\r\n  });\r\n\r\n  const loadMore = () => {\r\n    setVisibleBusinesses((prev) => Math.min(prev + 12, data.businesses.length));\r\n  };\r\n\r\n  const categories = [...new Set(data.businesses.map((b) => b.category))];\r\n\r\n  return (\r\n    <section className=\"business-directory\">\r\n      <div className=\"container py-4\">\r\n        {/* Enhanced Search Section */}\r\n        <div className=\"search-section bg-white p-4 rounded-3 shadow-sm mb-4\">\r\n          <div className=\"d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-3\">\r\n            <h4 className=\"mb-3 mb-md-0\">\r\n              <i className=\"fa fa-search me-2 text-primary\"></i>Find Businesses\r\n            </h4>\r\n            <div className=\"d-flex align-items-center\">\r\n              <div className=\"input-group me-3\" style={{ maxWidth: \"300px\" }}>\r\n                <span className=\"input-group-text bg-white border-end-0\">\r\n                  <i className=\"fa fa-filter text-muted\"></i>\r\n                </span>\r\n                <select\r\n                  className=\"form-select border-start-0\"\r\n                  value={activeFilter}\r\n                  onChange={(e) => handleFilterChange(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All Categories</option>\r\n                  {categories.map((category) => (\r\n                    <option key={category} value={category}>\r\n                      {category}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <button\r\n                className=\"btn btn-outline-secondary\"\r\n                onClick={() => {\r\n                  setSearch({\r\n                    name: \"\",\r\n                    suiteNumber: \"\",\r\n                    contactNumber: \"\",\r\n                    storeNumber: \"\",\r\n                  });\r\n                  setActiveFilter(\"all\");\r\n                }}\r\n              >\r\n                <i className=\"fa fa-refresh me-1\"></i>Reset\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"row g-3\">\r\n            {Object.keys(search).map((key) => (\r\n              <div key={key} className=\"col-md-3\">\r\n                <div className=\"input-group input-group-lg\">\r\n                  <span className=\"input-group-text bg-white\">\r\n                    <i\r\n                      className={`fa fa-${\r\n                        key === \"name\"\r\n                          ? \"building text-primary\"\r\n                          : key === \"suiteNumber\"\r\n                          ? \"map-marker text-danger\"\r\n                          : key === \"contactNumber\"\r\n                          ? \"phone text-success\"\r\n                          : \"hashtag text-warning\"\r\n                      }`}\r\n                    ></i>\r\n                  </span>\r\n                  <input\r\n                    type=\"text\"\r\n                    className=\"form-control border-start-0\"\r\n                    placeholder={`${key.replace(/([A-Z])/g, \" $1\")}...`}\r\n                    name={key}\r\n                    value={search[key]}\r\n                    onChange={handleSearchChange}\r\n                    style={{ fontSize: \"12px\" }}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"mt-3\">\r\n            <small className=\"text-muted me-2\">Quick search:</small>\r\n            {[\r\n              \"Insurance\",\r\n              \"Money Transfer\",\r\n              \"Restaurant\",\r\n              \"Salon\",\r\n              \"Grocery\",\r\n              \"Travel\",\r\n              \"Transportation\",\r\n            ].map((term) => (\r\n              <button\r\n                key={term}\r\n                className=\"btn btn-sm btn-outline-primary rounded-pill me-2 mb-1\"\r\n                onClick={() => setSearch({ ...search, name: term })}\r\n              >\r\n                {term}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Bar */}\r\n        <div className=\"stats-bar bg-light p-3 rounded-3 mb-4 shadow-sm\">\r\n          <div className=\"row text-center\">\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {data.businesses.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-building-o me-1\"></i> */}\r\n                Total Businesses\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {categories.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-tags me-1\"></i> */}\r\n                Categories\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3 border-end\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {new Set(data.businesses.map((b) => b.floor)).size}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                <i className=\"fa fa-layer-group me-1\"></i>\r\n                Floors\r\n              </p>\r\n            </div>\r\n            <div className=\"col-md-3\">\r\n              <h3 className=\"text-primary mb-0 animate-count\">\r\n                {filteredBusinesses.length}\r\n              </h3>\r\n              <p className=\"text-muted mb-0\">\r\n                {/* <i className=\"fa fa-check-circle me-1\"></i> */}\r\n                Matches Found\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Business Cards */}\r\n        <div className=\"row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4\">\r\n          {filteredBusinesses.slice(0, visibleBusinesses).map((business) => {\r\n            let logoPath = business.logo.startsWith(\"http\")\r\n              ? business.logo\r\n              : require(`../assets/${business.logo}`);\r\n\r\n            return (\r\n              <div key={business.id} className=\"col\">\r\n                <div\r\n                  className=\"card h-100 border-0 shadow-sm hover-shadow transition-all\"\r\n                  onClick={() => setSelectedBusiness(business)}\r\n                  style={{ cursor: \"pointer\" }}\r\n                >\r\n                  <div className=\"card-body\">\r\n                    <div className=\"d-flex align-items-center\">\r\n                      {logoPath ? (\r\n                        <img\r\n                          src={logoPath}\r\n                          alt={business.name}\r\n                          className=\"me-3 rounded\"\r\n                          title={business.name}\r\n                          style={{\r\n                            width: \"60px\",\r\n                            height: \"60px\",\r\n                            objectFit: \"contain\",\r\n                          }}\r\n                        />\r\n                      ) : (\r\n                        <div\r\n                          className=\"bg-light d-flex align-items-center justify-content-center me-3 rounded\"\r\n                          style={{\r\n                            width: \"60px\",\r\n                            height: \"60px\",\r\n                          }}\r\n                        >\r\n                          <i\r\n                            className=\"fa fa-building-o fa-2x\"\r\n                            style={{ color: \"#6c757d\" }}\r\n                          ></i>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"flex-grow-1\">\r\n                        <h5 className=\"mb-1\">{business.name}</h5>\r\n                        <p className=\"text-muted small mb-1\">\r\n                          {business.category && (\r\n                            <i className=\"fa fa-tag me-1\"></i>\r\n                          )}\r\n\r\n                          {/* {business.category} */}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text-end\">\r\n                        <span className=\"badge bg-primary\">\r\n                          <i className=\"fa fa-map-marker me-1\"></i>\r\n                          {business.suiteNumber}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"card-footer bg-transparent border-top\">\r\n                    <div className=\"d-flex justify-content-between\">\r\n                      <span className=\"small\">\r\n                        <i className=\"fa fa-phone me-1\"></i>\r\n                        {formatPhoneNumber(business.contactNumber)}\r\n                      </span>\r\n                      <span className=\"small\">\r\n                        {/* <i className=\"fa fa-map me-1\"></i> */}\r\n                        Floor {business.floor}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {visibleBusinesses < filteredBusinesses.length && (\r\n          <div className=\"text-center mt-4\">\r\n            <button\r\n              className=\"btn btn-primary btn-lg px-5 rounded-pill\"\r\n              onClick={loadMore}\r\n            >\r\n              <i className=\"fa fa-refresh me-2\"></i>Load More Businesses\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* No Results */}\r\n        {filteredBusinesses.length === 0 && (\r\n          <div className=\"text-center py-5 my-5\">\r\n            <i className=\"fa fa-frown-o fa-5x text-muted mb-4\"></i>\r\n            <h3>No Businesses Found</h3>\r\n            <p className=\"text-muted\">\r\n              Try adjusting your search criteria to find what you're looking\r\n              for.\r\n            </p>\r\n            <button\r\n              className=\"btn btn-outline-primary\"\r\n              onClick={() => {\r\n                setSearch({\r\n                  name: \"\",\r\n                  suiteNumber: \"\",\r\n                  contactNumber: \"\",\r\n                  storeNumber: \"\",\r\n                });\r\n                setActiveFilter(\"all\");\r\n              }}\r\n            >\r\n              <i className=\"fa fa-times me-1\"></i>Clear Filters\r\n            </button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Business Modal */}\r\n        {selectedBusiness && (\r\n          <div\r\n            className=\"modal fade show d-block\"\r\n            tabIndex=\"-1\"\r\n            role=\"dialog\"\r\n            style={{ backgroundColor: \"rgba(0, 0, 0, 0.5)\" }}\r\n          >\r\n            <div\r\n              className=\"modal-dialog modal-dialog-centered modal-lg\"\r\n              role=\"document\"\r\n            >\r\n              <div className=\"modal-content\">\r\n                <div className=\"modal-header bg-primary text-white\">\r\n                  <h3 className=\"modal-title\">\r\n                    <i className=\"fa fa-building-o me-2\"></i>\r\n                    {selectedBusiness.name}\r\n                  </h3>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"btn-close btn-close-white\"\r\n                    onClick={() => setSelectedBusiness(null)}\r\n                  ></button>\r\n                </div>\r\n                <div className=\"modal-body\">\r\n                  <div className=\"row\">\r\n                    <div className=\"col-md-4 text-center\">\r\n                      <img\r\n                        src={\r\n                          selectedBusiness.logo.startsWith(\"http\")\r\n                            ? selectedBusiness.logo\r\n                            : require(`../assets/${selectedBusiness.logo}`)\r\n                        }\r\n                        alt={selectedBusiness.name}\r\n                        className=\"img-fluid rounded mb-3 border p-2\"\r\n                        style={{\r\n                          maxHeight: \"200px\",\r\n                          objectFit: \"contain\",\r\n                        }}\r\n                      />\r\n                      <div className=\"d-grid gap-2\">\r\n                        <a\r\n                          href={`tel:${selectedBusiness.contactNumber}`}\r\n                          className=\"btn btn-outline-primary\"\r\n                        >\r\n                          <i className=\"fa fa-phone me-2\"></i>Call Now\r\n                        </a>\r\n                        <a\r\n                          href={`mailto:${selectedBusiness.email}`}\r\n                          className=\"btn btn-outline-secondary\"\r\n                        >\r\n                          <i className=\"fa fa-envelope me-2\"></i>Email\r\n                        </a>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"col-md-8\">\r\n                      <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">Business Details</h4>\r\n                        <div className=\"row\">\r\n                          <div className=\"col-md-6\">\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-map-marker text-primary me-2\"></i>\r\n                                Suite:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.suiteNumber}\r\n                            </p>\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-building text-primary me-2\"></i>\r\n                                Floor:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.floor}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"col-md-6\">\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-phone text-primary me-2\"></i>\r\n                                Phone:\r\n                              </strong>{\" \"}\r\n                              {formatPhoneNumber(\r\n                                selectedBusiness.contactNumber\r\n                              )}\r\n                            </p>\r\n                            <p>\r\n                              <strong>\r\n                                <i className=\"fa fa-envelope text-primary me-2\"></i>\r\n                                Email:\r\n                              </strong>{\" \"}\r\n                              {selectedBusiness.email || \"N/A\"}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">About</h4>\r\n                        <p>{selectedBusiness.description}</p>\r\n                      </div>\r\n\r\n                      {/* <div className=\"mb-4\">\r\n                        <h4 className=\"border-bottom pb-2\">Category</h4>\r\n                        <span className=\"badge bg-primary\">\r\n                          {selectedBusiness.category}\r\n                        </span>\r\n                      </div> */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"modal-footer\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"btn btn-secondary\"\r\n                    onClick={() => setSelectedBusiness(null)}\r\n                  >\r\n                    <i className=\"fa fa-times me-1\"></i>Close\r\n                  </button>\r\n                  <button type=\"button\" className=\"btn btn-primary\">\r\n                    <i className=\"fa fa-share-alt me-1\"></i>Share\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default BusinessCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACQ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMY,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAC3B,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,MAAMC,KAAK,GAAGF,OAAO,CAACE,KAAK,CAAC,yBAAyB,CAAC;IACtD,OAAOA,KAAK,GAAG,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGH,WAAW;EACtE,CAAC;EAED,MAAM,CAACI,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC;IACnCmB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChCN,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAE,CAACO,CAAC,CAACC,MAAM,CAACN,IAAI,GAAGK,CAAC,CAACC,MAAM,CAACC;IAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrCjB,eAAe,CAACiB,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAG5B,IAAI,CAAC6B,UAAU,CAACF,MAAM,CAAEG,QAAQ,IAAK;IAC9D,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACjB,MAAM,CAAC,CAACkB,KAAK,CAAEC,GAAG;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAChDN,QAAQ,CAACK,GAAG,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CACIC,QAAQ,CAAC,CAAC,CACXC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACvB,MAAM,CAACmB,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;IAAA,CACxC,CAAC;IACD,MAAME,aAAa,GACjB/B,YAAY,KAAK,KAAK,IAAIqB,QAAQ,CAACW,QAAQ,KAAKhC,YAAY;IAC9D,OAAOsB,WAAW,IAAIS,aAAa;EACrC,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBpC,oBAAoB,CAAEqC,IAAI,IAAKC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE3C,IAAI,CAAC6B,UAAU,CAACiB,MAAM,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChD,IAAI,CAAC6B,UAAU,CAACoB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACT,QAAQ,CAAC,CAAC,CAAC;EAEvE,oBACEvC,OAAA;IAASiD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrClD,OAAA;MAAKiD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7BlD,OAAA;QAAKiD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnElD,OAAA;UAAKiD,SAAS,EAAC,mFAAmF;UAAAC,QAAA,gBAChGlD,OAAA;YAAIiD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BlD,OAAA;cAAGiD,SAAS,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBACpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtD,OAAA;YAAKiD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClD,OAAA;cAAKiD,SAAS,EAAC,kBAAkB;cAACM,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAN,QAAA,gBAC7DlD,OAAA;gBAAMiD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACtDlD,OAAA;kBAAGiD,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACPtD,OAAA;gBACEiD,SAAS,EAAC,4BAA4B;gBACtC1B,KAAK,EAAEhB,YAAa;gBACpBkD,QAAQ,EAAGpC,CAAC,IAAKG,kBAAkB,CAACH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAA2B,QAAA,gBAEpDlD,OAAA;kBAAQuB,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CT,UAAU,CAACE,GAAG,CAAER,QAAQ,iBACvBvC,OAAA;kBAAuBuB,KAAK,EAAEgB,QAAS;kBAAAW,QAAA,EACpCX;gBAAQ,GADEA,QAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtD,OAAA;cACEiD,SAAS,EAAC,2BAA2B;cACrCS,OAAO,EAAEA,CAAA,KAAM;gBACb3C,SAAS,CAAC;kBACRC,IAAI,EAAE,EAAE;kBACRC,WAAW,EAAE,EAAE;kBACfC,aAAa,EAAE,EAAE;kBACjBC,WAAW,EAAE;gBACf,CAAC,CAAC;gBACFX,eAAe,CAAC,KAAK,CAAC;cACxB,CAAE;cAAA0C,QAAA,gBAEFlD,OAAA;gBAAGiD,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SACxC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAKiD,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrBpB,MAAM,CAACC,IAAI,CAACjB,MAAM,CAAC,CAACiC,GAAG,CAAEd,GAAG,iBAC3BjC,OAAA;YAAeiD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACjClD,OAAA;cAAKiD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzClD,OAAA;gBAAMiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACzClD,OAAA;kBACEiD,SAAS,EAAE,SACThB,GAAG,KAAK,MAAM,GACV,uBAAuB,GACvBA,GAAG,KAAK,aAAa,GACrB,wBAAwB,GACxBA,GAAG,KAAK,eAAe,GACvB,oBAAoB,GACpB,sBAAsB;gBACzB;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACPtD,OAAA;gBACE2D,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,6BAA6B;gBACvCW,WAAW,EAAE,GAAG3B,GAAG,CAACrB,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAM;gBACpDI,IAAI,EAAEiB,GAAI;gBACVV,KAAK,EAAET,MAAM,CAACmB,GAAG,CAAE;gBACnBwB,QAAQ,EAAErC,kBAAmB;gBAC7BmC,KAAK,EAAE;kBAAEM,QAAQ,EAAE;gBAAO;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC,GAxBErB,GAAG;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBR,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlD,OAAA;YAAOiD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACvD,CACC,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,SAAS,EACT,QAAQ,EACR,gBAAgB,CACjB,CAACP,GAAG,CAAEe,IAAI,iBACT9D,OAAA;YAEEiD,SAAS,EAAC,uDAAuD;YACjES,OAAO,EAAEA,CAAA,KAAM3C,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEE,IAAI,EAAE8C;YAAK,CAAC,CAAE;YAAAZ,QAAA,EAEnDY;UAAI,GAJAA,IAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKH,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKiD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DlD,OAAA;UAAKiD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlD,OAAA;YAAKiD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClD,OAAA;cAAIiD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CpD,IAAI,CAAC6B,UAAU,CAACiB;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACLtD,OAAA;cAAGiD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACqB;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClD,OAAA;cAAIiD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CL,UAAU,CAACD;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACLtD,OAAA;cAAGiD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACe;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClD,OAAA;cAAIiD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C,IAAIJ,GAAG,CAAChD,IAAI,CAAC6B,UAAU,CAACoB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACe,KAAK,CAAC,CAAC,CAACC;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACLtD,OAAA;cAAGiD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BlD,OAAA;gBAAGiD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlD,OAAA;cAAIiD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5CxB,kBAAkB,CAACkB;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACLtD,OAAA;cAAGiD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EACuB;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKiD,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAC5DxB,kBAAkB,CAACuC,KAAK,CAAC,CAAC,EAAE9D,iBAAiB,CAAC,CAAC4C,GAAG,CAAEnB,QAAQ,IAAK;UAChE,IAAIsC,QAAQ,GAAGtC,QAAQ,CAACuC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,GAC3CxC,QAAQ,CAACuC,IAAI,GACbE,OAAO,CAAC,aAAazC,QAAQ,CAACuC,IAAI,EAAE,CAAC;UAEzC,oBACEnE,OAAA;YAAuBiD,SAAS,EAAC,KAAK;YAAAC,QAAA,eACpClD,OAAA;cACEiD,SAAS,EAAC,2DAA2D;cACrES,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAACsB,QAAQ,CAAE;cAC7C2B,KAAK,EAAE;gBAAEe,MAAM,EAAE;cAAU,CAAE;cAAApB,QAAA,gBAE7BlD,OAAA;gBAAKiD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBlD,OAAA;kBAAKiD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GACvCgB,QAAQ,gBACPlE,OAAA;oBACEuE,GAAG,EAAEL,QAAS;oBACdM,GAAG,EAAE5C,QAAQ,CAACZ,IAAK;oBACnBiC,SAAS,EAAC,cAAc;oBACxBwB,KAAK,EAAE7C,QAAQ,CAACZ,IAAK;oBACrBuC,KAAK,EAAE;sBACLmB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdC,SAAS,EAAE;oBACb;kBAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFtD,OAAA;oBACEiD,SAAS,EAAC,wEAAwE;oBAClFM,KAAK,EAAE;sBACLmB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE;oBACV,CAAE;oBAAAzB,QAAA,eAEFlD,OAAA;sBACEiD,SAAS,EAAC,wBAAwB;sBAClCM,KAAK,EAAE;wBAAEsB,KAAK,EAAE;sBAAU;oBAAE;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,eACDtD,OAAA;oBAAKiD,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BlD,OAAA;sBAAIiD,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEtB,QAAQ,CAACZ;oBAAI;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzCtD,OAAA;sBAAGiD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACjCtB,QAAQ,CAACW,QAAQ,iBAChBvC,OAAA;wBAAGiD,SAAS,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAClC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACNtD,OAAA;oBAAKiD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBlD,OAAA;sBAAMiD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAChClD,OAAA;wBAAGiD,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACxC1B,QAAQ,CAACX,WAAW;oBAAA;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDlD,OAAA;kBAAKiD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7ClD,OAAA;oBAAMiD,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBACrBlD,OAAA;sBAAGiD,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnC7C,iBAAiB,CAACmB,QAAQ,CAACV,aAAa,CAAC;kBAAA;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACPtD,OAAA;oBAAMiD,SAAS,EAAC,OAAO;oBAAAC,QAAA,GACqB,QACpC,EAACtB,QAAQ,CAACmC,KAAK;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAhEE1B,QAAQ,CAACkD,EAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiEhB,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnD,iBAAiB,GAAGuB,kBAAkB,CAACkB,MAAM,iBAC5C5C,OAAA;QAAKiD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlD,OAAA;UACEiD,SAAS,EAAC,0CAA0C;UACpDS,OAAO,EAAElB,QAAS;UAAAU,QAAA,gBAElBlD,OAAA;YAAGiD,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,wBACxC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA5B,kBAAkB,CAACkB,MAAM,KAAK,CAAC,iBAC9B5C,OAAA;QAAKiD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClD,OAAA;UAAGiD,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDtD,OAAA;UAAAkD,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BtD,OAAA;UAAGiD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAG1B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtD,OAAA;UACEiD,SAAS,EAAC,yBAAyB;UACnCS,OAAO,EAAEA,CAAA,KAAM;YACb3C,SAAS,CAAC;cACRC,IAAI,EAAE,EAAE;cACRC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,EAAE;cACjBC,WAAW,EAAE;YACf,CAAC,CAAC;YACFX,eAAe,CAAC,KAAK,CAAC;UACxB,CAAE;UAAA0C,QAAA,gBAEFlD,OAAA;YAAGiD,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAjD,gBAAgB,iBACfL,OAAA;QACEiD,SAAS,EAAC,yBAAyB;QACnC8B,QAAQ,EAAC,IAAI;QACbC,IAAI,EAAC,QAAQ;QACbzB,KAAK,EAAE;UAAE0B,eAAe,EAAE;QAAqB,CAAE;QAAA/B,QAAA,eAEjDlD,OAAA;UACEiD,SAAS,EAAC,6CAA6C;UACvD+B,IAAI,EAAC,UAAU;UAAA9B,QAAA,eAEflD,OAAA;YAAKiD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlD,OAAA;cAAKiD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDlD,OAAA;gBAAIiD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACzBlD,OAAA;kBAAGiD,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxCjD,gBAAgB,CAACW,IAAI;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACLtD,OAAA;gBACE2D,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,2BAA2B;gBACrCS,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,IAAI;cAAE;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNtD,OAAA;cAAKiD,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlD,OAAA;gBAAKiD,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBlD,OAAA;kBAAKiD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnClD,OAAA;oBACEuE,GAAG,EACDlE,gBAAgB,CAAC8D,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,GACpC/D,gBAAgB,CAAC8D,IAAI,GACrBE,OAAO,CAAC,aAAahE,gBAAgB,CAAC8D,IAAI,EAAE,CACjD;oBACDK,GAAG,EAAEnE,gBAAgB,CAACW,IAAK;oBAC3BiC,SAAS,EAAC,mCAAmC;oBAC7CM,KAAK,EAAE;sBACL2B,SAAS,EAAE,OAAO;sBAClBN,SAAS,EAAE;oBACb;kBAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFtD,OAAA;oBAAKiD,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlD,OAAA;sBACEmF,IAAI,EAAE,OAAO9E,gBAAgB,CAACa,aAAa,EAAG;sBAC9C+B,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBAEnClD,OAAA;wBAAGiD,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YACtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJtD,OAAA;sBACEmF,IAAI,EAAE,UAAU9E,gBAAgB,CAAC+E,KAAK,EAAG;sBACzCnC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBAErClD,OAAA;wBAAGiD,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SACzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtD,OAAA;kBAAKiD,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBlD,OAAA;oBAAKiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlD,OAAA;sBAAIiD,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxDtD,OAAA;sBAAKiD,SAAS,EAAC,KAAK;sBAAAC,QAAA,gBAClBlD,OAAA;wBAAKiD,SAAS,EAAC,UAAU;wBAAAC,QAAA,gBACvBlD,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAAkD,QAAA,gBACElD,OAAA;8BAAGiD,SAAS,EAAC;4BAAoC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAExD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZjD,gBAAgB,CAACY,WAAW;wBAAA;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,eACJtD,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAAkD,QAAA,gBACElD,OAAA;8BAAGiD,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEtD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZjD,gBAAgB,CAAC0D,KAAK;wBAAA;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNtD,OAAA;wBAAKiD,SAAS,EAAC,UAAU;wBAAAC,QAAA,gBACvBlD,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAAkD,QAAA,gBACElD,OAAA;8BAAGiD,SAAS,EAAC;4BAA+B;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEnD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZ7C,iBAAiB,CAChBJ,gBAAgB,CAACa,aACnB,CAAC;wBAAA;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACJtD,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAAkD,QAAA,gBACElD,OAAA;8BAAGiD,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,UAEtD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAAC,GAAG,EACZjD,gBAAgB,CAAC+E,KAAK,IAAI,KAAK;wBAAA;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtD,OAAA;oBAAKiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlD,OAAA;sBAAIiD,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7CtD,OAAA;sBAAAkD,QAAA,EAAI7C,gBAAgB,CAACgF;oBAAW;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtD,OAAA;cAAKiD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlD,OAAA;gBACE2D,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,mBAAmB;gBAC7BS,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,IAAI,CAAE;gBAAA4C,QAAA,gBAEzClD,OAAA;kBAAGiD,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,SACtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtD,OAAA;gBAAQ2D,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC/ClD,OAAA;kBAAGiD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,SAC1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACpD,EAAA,CA5aID,YAAY;AAAAqF,EAAA,GAAZrF,YAAY;AA8alB,eAAeA,YAAY;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}