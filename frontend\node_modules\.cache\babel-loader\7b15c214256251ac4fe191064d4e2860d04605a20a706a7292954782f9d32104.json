{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\kbc-mall\\\\frontend\\\\src\\\\footer\\\\Footer.js\";\nimport React from \"react\";\nimport \"./FooterStyle.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"mt-4 bg-lightxx position-absolutexx bottom-0 w-full text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\" \", \"\\xA9 \", new Date().getFullYear(), \", Copyright. KBCMALL - Business Directory. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "children", "className", "Date", "getFullYear", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/kbc-mall/frontend/src/footer/Footer.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./FooterStyle.css\";\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <div>\r\n      <footer className=\"mt-4 bg-lightxx position-absolutexx bottom-0 w-full text-center\">\r\n        <div className=\"container\">\r\n          <p className=\"text-muted\">\r\n            {\" \"}\r\n            &copy; {new Date().getFullYear()}, Copyright. KBCMALL - Business\r\n            Directory. All rights reserved. \r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAAE,QAAA,eACEF,OAAA;MAAQG,SAAS,EAAC,iEAAiE;MAAAD,QAAA,eACjFF,OAAA;QAAKG,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBF,OAAA;UAAGG,SAAS,EAAC,YAAY;UAAAD,QAAA,GACtB,GAAG,EAAC,OACE,EAAC,IAAIE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,iEAEnC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GAdIT,MAAM;AAgBZ,eAAeA,MAAM;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}