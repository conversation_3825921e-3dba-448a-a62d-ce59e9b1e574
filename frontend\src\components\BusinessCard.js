import React, { useState, useMemo, useCallback } from "react";
import data from "./data.json";
import "./BusinessCard.css";

const BusinessCard = () => {
  const [visibleBusinesses, setVisibleBusinesses] = useState(12);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [activeFilter, setActiveFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(false);

  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return "";
    const cleaned = phoneNumber.replace(/\D/g, "");
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phoneNumber;
  };

  const [search, setSearch] = useState({
    name: "",
    suiteNumber: "",
    contactNumber: "",
    storeNumber: "",
  });

  const handleSearchChange = useCallback((e) => {
    setSearch((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  }, []);

  const handleFilterChange = useCallback((filter) => {
    setActiveFilter(filter);
  }, []);

  const filteredBusinesses = useMemo(() => {
    return data.businesses.filter((business) => {
      const searchMatch = Object.keys(search).every((key) =>
        business[key]
          ?.toString()
          .toLowerCase()
          .includes(search[key].toLowerCase())
      );

      const filterMatch =
        activeFilter === "all" ||
        (activeFilter.startsWith("floor") &&
          business.floor === parseInt(activeFilter.replace("floor", ""))) ||
        business.category === activeFilter;

      return searchMatch && filterMatch;
    });
  }, [search, activeFilter]);

  const loadMore = useCallback(() => {
    setVisibleBusinesses((prev) => Math.min(prev + 12, data.businesses.length));
  }, []);

  const floors = useMemo(
    () => [...new Set(data.businesses.map((b) => b.floor))].sort(),
    []
  );

  const categories = useMemo(
    () => [...new Set(data.businesses.map((b) => b.category).filter(Boolean))],
    []
  );

  return (
    <section className="business-directory">
      <div className="container py-4">
        {/* Modern Search Section */}
        <div className="modern-search-section">
          <div className="search-header">
            <h2 className="search-title">
              <i className="fa fa-search search-icon"></i>
              Discover Businesses
            </h2>
            <p className="search-subtitle">
              Find the perfect business for your needs
            </p>
          </div>

          <div className="search-controls">
            <div className="filter-section">
              <label className="filter-label">Filter Options</label>
              <div className="filter-buttons">
                <button
                  className={`filter-btn ${
                    activeFilter === "all" ? "active" : ""
                  }`}
                  onClick={() => handleFilterChange("all")}
                >
                  All
                </button>
                {floors.map((floor) => (
                  <button
                    key={floor}
                    className={`filter-btn ${
                      activeFilter === `floor${floor}` ? "active" : ""
                    }`}
                    onClick={() => handleFilterChange(`floor${floor}`)}
                  >
                    Floor {floor}
                  </button>
                ))}
                {categories.map((category) => (
                  <button
                    key={category}
                    className={`filter-btn ${
                      activeFilter === category ? "active" : ""
                    }`}
                    onClick={() => handleFilterChange(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            <button
              className="reset-btn"
              onClick={() => {
                setSearch({
                  name: "",
                  suiteNumber: "",
                  contactNumber: "",
                  storeNumber: "",
                });
                setActiveFilter("all");
              }}
              aria-label="Reset all filters and search"
            >
              <i className="fa fa-refresh"></i>
              Reset All
            </button>
          </div>

          <div className="search-inputs">
            {Object.keys(search).map((key) => (
              <div key={key} className="search-input-group">
                <label className="input-label">
                  <i
                    className={`fa fa-${
                      key === "name"
                        ? "building"
                        : key === "suiteNumber"
                        ? "map-marker"
                        : key === "contactNumber"
                        ? "phone"
                        : "hashtag"
                    } input-icon`}
                  ></i>
                  {key === "name"
                    ? "Business Name"
                    : key === "suiteNumber"
                    ? "Suite Number"
                    : key === "contactNumber"
                    ? "Contact Number"
                    : "Store Number"}
                </label>
                <input
                  type="text"
                  className="modern-input"
                  placeholder={`Enter ${
                    key === "name"
                      ? "business name"
                      : key === "suiteNumber"
                      ? "suite number"
                      : key === "contactNumber"
                      ? "contact number"
                      : "store number"
                  }...`}
                  name={key}
                  value={search[key]}
                  onChange={handleSearchChange}
                  aria-label={`Search by ${
                    key === "name"
                      ? "business name"
                      : key === "suiteNumber"
                      ? "suite number"
                      : key === "contactNumber"
                      ? "contact number"
                      : "store number"
                  }`}
                />
              </div>
            ))}
          </div>

          <div className="quick-search">
            <span className="quick-search-label">Quick search:</span>
            <div className="quick-search-buttons">
              {[
                "Insurance",
                "Money Transfer",
                "Restaurant",
                "Salon",
                "Grocery",
                "Travel",
                "Transportation",
              ].map((term) => (
                <button
                  key={term}
                  className="quick-search-btn"
                  onClick={() => setSearch({ ...search, name: term })}
                >
                  {term}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Modern Stats Section */}
        <div className="stats-section">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <i className="fa fa-building-o"></i>
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{data.businesses.length}</h3>
                <p className="stat-label">Total Businesses</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <i className="fa fa-tags"></i>
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{categories.length}</h3>
                <p className="stat-label">Categories</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <i className="fa fa-layer-group"></i>
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{floors.length}</h3>
                <p className="stat-label">Floors</p>
              </div>
            </div>
            <div className="stat-card highlight">
              <div className="stat-icon">
                <i className="fa fa-check-circle"></i>
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{filteredBusinesses.length}</h3>
                <p className="stat-label">Matches Found</p>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Business Cards */}
        <div className="business-cards-grid">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="business-card skeleton">
                <div className="card-header">
                  <div className="skeleton-logo"></div>
                  <div className="skeleton-content">
                    <div className="skeleton-title"></div>
                    <div className="skeleton-subtitle"></div>
                  </div>
                  <div className="skeleton-badge"></div>
                </div>
                <div className="card-body">
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line short"></div>
                </div>
              </div>
            ))
          ) : filteredBusinesses.length === 0 ? (
            // Empty state
            <div className="empty-state">
              <div className="empty-icon">
                <i className="fa fa-search"></i>
              </div>
              <h3>No businesses found</h3>
              <p>Try adjusting your search criteria or filters</p>
              <button
                className="reset-btn"
                onClick={() => {
                  setSearch({
                    name: "",
                    suiteNumber: "",
                    contactNumber: "",
                    storeNumber: "",
                  });
                  setActiveFilter("all");
                }}
              >
                Reset Search
              </button>
            </div>
          ) : (
            filteredBusinesses.slice(0, visibleBusinesses).map((business) => {
              let logoPath;
              try {
                logoPath = business.logo.startsWith("http")
                  ? business.logo
                  : require(`../assets/${business.logo}`);
              } catch (error) {
                logoPath = null;
              }

              return (
                <div
                  key={business.id}
                  className="business-card"
                  onClick={() => setSelectedBusiness(business)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      setSelectedBusiness(business);
                    }
                  }}
                  aria-label={`View details for ${business.name}`}
                >
                  <div className="card-header">
                    <div className="business-logo">
                      {logoPath ? (
                        <img
                          src={logoPath}
                          alt={`${business.name} logo`}
                          className="logo-image"
                        />
                      ) : (
                        <div className="logo-placeholder">
                          <i className="fa fa-building-o"></i>
                        </div>
                      )}
                    </div>
                    <div className="business-info">
                      <h3 className="business-name">{business.name}</h3>
                      {business.category && (
                        <p className="business-category">
                          <i className="fa fa-tag"></i>
                          {business.category}
                        </p>
                      )}
                    </div>
                    <div className="suite-badge">
                      <i className="fa fa-map-marker"></i>
                      {business.suiteNumber}
                    </div>
                  </div>
                  <div className="card-body">
                    <div className="contact-info">
                      <div className="contact-item">
                        <i className="fa fa-phone"></i>
                        <span>{formatPhoneNumber(business.contactNumber)}</span>
                      </div>
                      <div className="contact-item">
                        <i className="fa fa-building"></i>
                        <span>Floor {business.floor}</span>
                      </div>
                      {business.email && (
                        <div className="contact-item">
                          <i className="fa fa-envelope"></i>
                          <span>{business.email}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="card-footer">
                    <button className="view-details-btn">
                      <i className="fa fa-eye"></i>
                      View Details
                    </button>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {visibleBusinesses < filteredBusinesses.length && (
          <div className="load-more-section">
            <button
              className="load-more-btn"
              onClick={loadMore}
              aria-label={`Load more businesses. ${
                filteredBusinesses.length - visibleBusinesses
              } remaining`}
            >
              <i className="fa fa-plus-circle"></i>
              Load More ({filteredBusinesses.length - visibleBusinesses}{" "}
              remaining)
            </button>
          </div>
        )}

        {/* Modern Business Modal */}
        {selectedBusiness && (
          <div
            className="modern-modal-overlay"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setSelectedBusiness(null);
              }
            }}
          >
            <div className="modern-modal">
              <div className="modal-header">
                <div className="modal-title-section">
                  <h2 className="modal-title">{selectedBusiness.name}</h2>
                  {selectedBusiness.category && (
                    <span className="modal-category">
                      <i className="fa fa-tag"></i>
                      {selectedBusiness.category}
                    </span>
                  )}
                </div>
                <button
                  className="modal-close-btn"
                  onClick={() => setSelectedBusiness(null)}
                  aria-label="Close modal"
                >
                  <i className="fa fa-times"></i>
                </button>

              <div className="modal-body">
                <div className="modal-content-grid">
                  <div className="modal-logo-section">
                    {(() => {
                      let logoPath;
                      try {
                        logoPath = selectedBusiness.logo.startsWith("http")
                          ? selectedBusiness.logo
                          : require(`../assets/${selectedBusiness.logo}`);
                      } catch (error) {
                        logoPath = null;
                      }

                      return logoPath ? (
                        <img
                          src={logoPath}
                          alt={`${selectedBusiness.name} logo`}
                          className="modal-logo"
                        />
                      ) : (
                        <div className="modal-logo-placeholder">
                          <i className="fa fa-building-o"></i>
                        </div>
                      );
                    })()}

                    <div className="modal-actions">
                      <a
                        href={`tel:${selectedBusiness.contactNumber}`}
                        className="modal-action-btn primary"
                      >
                        <i className="fa fa-phone"></i>
                        Call Now
                      </a>
                      {selectedBusiness.email && (
                        <a
                          href={`mailto:${selectedBusiness.email}`}
                          className="modal-action-btn secondary"
                        >
                          <i className="fa fa-envelope"></i>
                          Email
                        </a>
                      )}
                    </div>
                  </div>

                  <div className="modal-details-section">
                    <div className="detail-group">
                      <h3 className="detail-title">Location</h3>
                      <div className="detail-items">
                        <div className="detail-item">
                          <i className="fa fa-map-marker"></i>
                          <span>Suite {selectedBusiness.suiteNumber}</span>
                        </div>
                        <div className="detail-item">
                          <i className="fa fa-building"></i>
                          <span>Floor {selectedBusiness.floor}</span>
                        </div>
                      </div>
                    </div>

                    <div className="detail-group">
                      <h3 className="detail-title">Contact</h3>
                      <div className="detail-items">
                        <div className="detail-item">
                          <i className="fa fa-phone"></i>
                          <span>{formatPhoneNumber(selectedBusiness.contactNumber)}</span>
                        </div>
                        {selectedBusiness.email && (
                          <div className="detail-item">
                            <i className="fa fa-envelope"></i>
                            <span>{selectedBusiness.email}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {selectedBusiness.description && (
                      <div className="detail-group">
                        <h3 className="detail-title">About</h3>
                        <p className="detail-description">{selectedBusiness.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default BusinessCard;
