{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromCodePoint = String.fromCodePoint || function (astralCodePoint) {\n  return String.fromCharCode(Math.floor((astralCodePoint - 65536) / 1024) + 55296, (astralCodePoint - 65536) % 1024 + 56320);\n};\nexports.getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 55296) * 1024 + input.charCodeAt(position + 1) - 56320 + 65536;\n};\nexports.highSurrogateFrom = 55296;\nexports.highSurrogateTo = 56319;", "map": {"version": 3, "names": ["exports", "fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo"], "sources": ["C:\\Users\\<USER>\\Documents\\kbc-mall\\frontend\\node_modules\\html-entities\\src\\surrogate-pairs.ts"], "sourcesContent": ["export const fromCodePoint =\n    String.fromCodePoint ||\n    function (astralCodePoint: number) {\n        return String.fromCharCode(\n            Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800,\n            ((astralCodePoint - 0x10000) % 0x400) + 0xdc00\n        );\n    };\n\nexport const getCodePoint = String.prototype.codePointAt\n    ? function (input: string, position: number) {\n          return input.codePointAt(position);\n      }\n    : function (input: string, position: number) {\n          return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n      };\n\nexport const highSurrogateFrom = 0xd800;\nexport const highSurrogateTo = 0xdbff;\n"], "mappings": ";;;;;AAAaA,OAAA,CAAAC,aAAA,GACTC,MAAA,CAAOD,aAAA,IACP,UAAUE,eAAA;EACN,OAAOD,MAAA,CAAOE,YAAA,CACVC,IAAA,CAAKC,KAAA,EAAOH,eAAA,GAAkB,SAAW,QAAS,QAChDA,eAAA,GAAkB,SAAW,OAAS,MAEhD;AAAA;AAESH,OAAA,CAAAO,YAAA,GAAeL,MAAA,CAAOM,SAAA,CAAUC,WAAA,GACvC,UAAUC,KAAA,EAAeC,QAAA;EACrB,OAAOD,KAAA,CAAMD,WAAA,CAAYE,QAAA,CAC7B;AAAA,IACA,UAAUD,KAAA,EAAeC,QAAA;EACrB,QAAQD,KAAA,CAAME,UAAA,CAAWD,QAAA,IAAY,SAAU,OAAQD,KAAA,CAAME,UAAA,CAAWD,QAAA,GAAW,KAAK,QAAS,KACrG;AAAA;AAEOX,OAAA,CAAAa,iBAAA,GAAoB;AACpBb,OAAA,CAAAc,eAAA,GAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}