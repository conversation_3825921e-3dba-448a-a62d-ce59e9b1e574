/* Modern Business Directory Styles */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Global styles */
.business-directory {
  background: var(--background-color);
  min-height: 100vh;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
}

/* Modern Search Section */
.modern-search-section {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.modern-search-section:hover {
  box-shadow: var(--shadow-lg);
}

.search-header {
  text-align: center;
  margin-bottom: 2rem;
}

.search-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.search-icon {
  color: var(--primary-color);
  font-size: 1.75rem;
}

.search-subtitle {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0;
}

.search-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 2rem;
  gap: 2rem;
}

.filter-section {
  flex: 1;
}

.filter-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-btn {
  background: var(--border-light);
  border: 2px solid transparent;
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.filter-btn:hover {
  background: var(--border-color);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-dark);
  box-shadow: var(--shadow-sm);
}

.reset-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.reset-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Search Inputs */
.search-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.search-input-group {
  display: flex;
  flex-direction: column;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.input-icon {
  color: var(--primary-color);
  width: 1rem;
}

.modern-input {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-primary);
  transition: var(--transition-fast);
  width: 100%;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.modern-input::placeholder {
  color: var(--text-muted);
}

/* Quick Search */
.quick-search {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-light);
}

.quick-search-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  white-space: nowrap;
}

.quick-search-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.quick-search-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.quick-search-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card.highlight {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: white;
  border-color: var(--primary-dark);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--border-light);
  color: var(--primary-color);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-card.highlight .stat-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1;
}

.stat-card.highlight .stat-number {
  color: white;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

.stat-card.highlight .stat-label {
  color: rgba(255, 255, 255, 0.9);
}

/* Business Cards Grid */
.business-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Individual Business Card */
.business-card {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
}

.business-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-color);
}

.business-card:focus {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(37, 99, 235, 0.1);
  border-color: var(--primary-color);
}

/* Card Header */
.card-header {
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.business-logo {
  width: 4rem;
  height: 4rem;
  flex-shrink: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: white;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 1.5rem;
}

.business-info {
  flex: 1;
  min-width: 0;
}

.business-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  word-wrap: break-word;
}

.business-category {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

.business-category i {
  color: var(--primary-color);
}

.suite-badge {
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  white-space: nowrap;
}

/* Card Body */
.card-body {
  padding: 1.5rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.contact-item i {
  width: 1rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.contact-item span {
  font-weight: 500;
}

/* Card Footer */
.card-footer {
  padding: 1rem 1.5rem;
  background: var(--border-light);
  border-top: 1px solid var(--border-color);
}

.view-details-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
}

.view-details-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Skeleton Loading */
.business-card.skeleton {
  cursor: default;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.business-card.skeleton:hover {
  transform: none;
  box-shadow: var(--shadow-sm);
  border-color: var(--border-light);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.skeleton-logo {
  width: 4rem;
  height: 4rem;
  background: var(--border-color);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-title {
  height: 1.5rem;
  background: var(--border-color);
  border-radius: var(--radius-sm);
  width: 70%;
}

.skeleton-subtitle {
  height: 1rem;
  background: var(--border-color);
  border-radius: var(--radius-sm);
  width: 50%;
}

.skeleton-badge {
  width: 4rem;
  height: 2rem;
  background: var(--border-color);
  border-radius: var(--radius-lg);
}

.skeleton-line {
  height: 1rem;
  background: var(--border-color);
  border-radius: var(--radius-sm);
  width: 100%;
}

.skeleton-line.short {
  width: 60%;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--border-color);
}

.empty-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  font-size: 2rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: var(--text-secondary);
  margin: 0 0 2rem 0;
  font-size: 1rem;
}

/* Load More Section */
.load-more-section {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.load-more-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--radius-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-sm);
}

.load-more-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.load-more-btn:active {
  transform: translateY(0);
}

/* Modern Modal */
.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modern-modal {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 2rem 2rem 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--border-light);
}

.modal-title-section {
  flex: 1;
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.modal-category {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  background: var(--primary-color);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.modal-close-btn {
  background: var(--border-light);
  border: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.modal-close-btn:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.modal-body {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-content-grid {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
}

.modal-logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.modal-logo {
  width: 150px;
  height: 150px;
  object-fit: contain;
  border-radius: var(--radius-lg);
  background: white;
  border: 1px solid var(--border-light);
  padding: 1rem;
}

.modal-logo-placeholder {
  width: 150px;
  height: 150px;
  background: var(--border-light);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 3rem;
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.modal-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  text-decoration: none;
  transition: var(--transition-fast);
  border: 2px solid transparent;
}

.modal-action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.modal-action-btn.primary:hover {
  background: var(--primary-dark);
  color: white;
  transform: translateY(-1px);
}

.modal-action-btn.secondary {
  background: var(--border-light);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.modal-action-btn.secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.modal-details-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-light);
}

.detail-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

.detail-item i {
  width: 1.25rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.detail-item span {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Search Section */
  .modern-search-section {
    padding: 1.5rem;
  }

  .search-title {
    font-size: 1.5rem;
  }

  .search-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .filter-buttons {
    justify-content: center;
  }

  .search-inputs {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-search {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  /* Stats Grid */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  /* Business Cards */
  .business-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .business-card {
    margin: 0 0.5rem;
  }

  .card-header {
    padding: 1rem;
  }

  .business-logo {
    width: 3rem;
    height: 3rem;
  }

  .business-name {
    font-size: 1.125rem;
  }

  .card-body {
    padding: 1rem;
  }

  .card-footer {
    padding: 0.75rem 1rem;
  }

  /* Modal */
  .modern-modal {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modal-logo-section {
    order: 2;
  }

  .modal-details-section {
    order: 1;
  }

  .modal-logo,
  .modal-logo-placeholder {
    width: 120px;
    height: 120px;
  }

  .modal-actions {
    flex-direction: row;
  }
}

@media (max-width: 480px) {
  .modern-search-section {
    padding: 1rem;
  }

  .search-title {
    font-size: 1.25rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-buttons {
    gap: 0.375rem;
  }

  .filter-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  .business-card {
    margin: 0;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-icon {
    width: 4rem;
    height: 4rem;
    font-size: 1.5rem;
  }
}

/* Print Styles */
@media print {
  .modern-search-section,
  .stats-section,
  .load-more-section {
    display: none;
  }

  .business-cards-grid {
    display: block;
  }

  .business-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .business-card:hover {
    transform: none;
    box-shadow: none;
  }

  .view-details-btn {
    display: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --border-light: #666666;
    --text-muted: #333333;
  }

  .business-card {
    border-width: 2px;
  }

  .filter-btn {
    border-width: 2px;
    border-color: var(--border-color);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .business-card:hover {
    transform: none;
  }

  .load-more-btn:hover,
  .view-details-btn:hover,
  .modal-action-btn:hover {
    transform: none;
  }
}
